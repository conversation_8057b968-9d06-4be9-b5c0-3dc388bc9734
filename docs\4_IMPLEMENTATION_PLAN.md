# 4. Project Governance & Delivery Strategy

## Purpose

To outline the processes, policies, and project management approach for **OuiLink**, ensuring quality, consistency, and alignment with business goals. This document governs *how* we build, while the Roadmap (`7_TRACKING_PLANNING.md`) defines *what* we build.

## Project Phases

1.  **Phase 0: Foundation & Planning (Current)**
    - Finalize documentation (Requirements, Tech Stack, Roadmap).
    - Set up repositories, CI/CD pipeline, and development environments.
2.  **Phase 1: MVP Development**
    - Execute the sprints/steps outlined in the `7_TRACKING_PLANNING.md`.
    - Conduct bi-weekly sprint reviews to demo progress and gather feedback.
3.  **Phase 2: Alpha Testing**
    - Internal testing with the core team and a small, trusted group of pilot users.
    - Focus on bug fixing, performance tuning, and UX refinements.
4.  **Phase 3: Beta Launch**
    - Controlled release to a wider, invitation-only group of users from our target audience.
    - Gather quantitative and qualitative feedback to validate the solution.
5.  **Phase 4: Public Launch & Iteration**
    - Go-live for the general public in Senegal.
    - Transition from project mode to a continuous iteration cycle based on user data and feedback.

## Risk Management

| Risk Category         | Description                                                                 | Mitigation Strategy                                                                                               |
|-----------------------|-----------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------|
| **Technical Risk**    | Integration with local payment gateway (PayDunya) proves difficult.         | Create a technical spike early in development. Have Stripe as a well-documented backup plan.                      |
| **User Adoption Risk**| Vacataires are hesitant to upload sensitive documents.                      | Clearly communicate security measures (encryption, access control). Implement a "Verified" badge as a strong incentive. |
| **Market Risk**       | Incumbent informal networks (WhatsApp) are too entrenched.                  | Focus on superior value: security, guaranteed payments, digital contracts, and professional reputation building. |
| **Execution Risk**    | Development timeline slips due to scope creep.                              | Strictly adhere to the MVP scope. All changes must be approved and logged in `6_DECISION_LOG.md`.                |

## Test-Driven Development (TDD) & Pull Request (PR) Policy

- **TDD is Mandatory:**
  1.  **Red:** Write a failing test that clearly defines a new feature or improvement.
  2.  **Green:** Write the simplest code possible to make the test pass.
  3.  **Refactor:** Clean up the code while ensuring all tests still pass.
- **Test Naming Convention:** Tests must be descriptive, following the pattern `test_use_case_should_expected_behavior_when_condition`.
- **Pull Request (PR) Policy:**
  1.  All work must be done on a feature branch.
  2.  A PR must be linked to a specific task or user story.
  3.  The PR description must explain the "what" and "why" of the changes.
  4.  All CI checks (linting, tests) must pass.
  5.  At least one other developer must review and approve the PR before merging to the main branch.
  6.  AI may assist in generating code but **must not** merge PRs automatically.

## AI Firewalls (Red Flags)

- Do NOT merge code that breaks existing tests or reduces test coverage.
- Do NOT release features to users without passing through the defined Alpha/Beta testing phases.
- Do NOT ignore the PR review process for the sake of speed. Quality and stability are paramount.


---

_Reference this document for project phases, milestones, and delivery strategy. See also: Specs fonctionnelles MVP, Business Plan._
