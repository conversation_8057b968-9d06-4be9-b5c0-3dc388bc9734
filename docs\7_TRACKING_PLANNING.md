# 7. MVP Implementation Roadmap (ORD)

## Purpose

This document serves as the **Operational Requirements Document (ORD)** and master implementation plan for the **OuiLink MVP**. It breaks down all required features into a sequential, actionable roadmap with clear tasks for backend and frontend teams. Development should follow these steps in order.

---

### **Sprint 0: Project Foundation & DevOps**

- **Goal:** Prepare the development environment, CI/CD, and core project structure.
- **Backend Tasks:**
  - `Task-B0.1`: Initialize Python/Flask project with Clean Architecture structure (`core`, `entry_points`, `infrastructure`).
  - `Task-B0.2`: Set up Docker for local development (PostgreSQL).
  - `Task-B0.3`: Configure SQLAlchemy, Alembic for migrations, and connect to the database.
  - `Task-B0.4`: Implement basic CI/CD pipeline (linting, testing) with GitHub Actions.
- **Frontend Tasks:**
  - `Task-F0.1`: Initialize Vue.js project with <PERSON>uetify and <PERSON><PERSON> for state management.
  - `Task-F0.2`: Set up project structure (`views`, `components`, `services`, `store`).
  - `Task-F0.3`: Configure environment variables for API endpoints.
  - `Task-F0.4`: Implement basic CI pipeline for linting and testing.

### **Sprint 1: User Identity & Authentication**

- **Goal:** Enable secure user registration and login for both Vacataires and Employers.
- **Backend Tasks:**
  - `Task-B1.1`: Model `User` and `Role` domains.
  - `Task-B1.2`: Integrate SuperTokens core for session management and user ID.
  - `Task-B1.3`: Implement API endpoints for registration (email/password), login, logout, and session verification.
  - `Task-B1.4`: Write unit and integration tests for all auth flows.
- **Frontend Tasks:**
  - `Task-F1.1`: Integrate SuperTokens pre-built UI for Login and Registration forms.
  - `Task-F1.2`: Implement route guards for authenticated/unauthenticated users.
  - `Task-F1.3`: Set up global state management (Pinia) for user session info.
  - `Task-F1.4`: Create placeholder pages for a logged-in dashboard and a public landing page.

### **Sprint 2: Profile Management & Document Vault**

- **Goal:** Allow users to build their profiles and upload verification documents.
- **Dependencies:** Sprint 1.
- **Backend Tasks:**
  - `Task-B2.1`: Extend `User` model for profile fields (name, specialty, location, bio).
  - `Task-B2.2`: Create `Document` model (type, URL, verification_status).
  - `Task-B2.3`: Implement API endpoints for Profile CRUD.
  - `Task-B2.4`: Implement secure endpoint for generating an S3 pre-signed URL for document upload.
  - `Task-B2.5`: Implement an endpoint to record the uploaded document's URL and set its status to "Pending Verification".
- **Frontend Tasks:**
  - `Task-F2.1`: Build a `ProfileView` page for viewing and editing user information.
  - `Task-F2.2`: Create a `DocumentUpload` component that fetches the pre-signed URL and uploads the file directly to S3.
  - `Task-F2.3`: Build a `DocumentList` component to display uploaded documents and their verification status.
  - `Task-F2.4`: Implement UI indicators for profile completeness.

### **Sprint 3: Mission Posting & Search (Core Loop)**

- **Goal:** Enable employers to post missions and vacataires to find them.
- **Dependencies:** Sprint 1, 2.
- **Backend Tasks:**
  - `Task-B3.1`: Implement `Mission` model (title, description, specialty, dates, budget, status).
  - `Task-B3.2`: Implement full CRUD API endpoints for missions (for employers).
  - `Task-B3.3`: Implement a public, searchable/filterable API endpoint for missions (for vacataires).
  - `Task-B3.4`: Implement `Application` model linking `User` and `Mission`.
  - `Task-B3.5`: Implement API endpoints to apply to a mission, withdraw an application, and list applicants for a mission.
- **Frontend Tasks:**
  - `Task-F3.1`: (Employer) Build `MissionCreateEdit` form with validation.
  - `Task-F3.2`: (Employer) Build `MissionDashboard` to list created missions and view applicants.
  - `Task-F3.3`: (Vacataire) Build `MissionSearchView` with filters for location, specialty, and date.
  - `Task-F3.4`: (Vacataire) Implement the application flow UI (Apply button, view status).

### **Sprint 4: Digital Contracting & E-Signature**

- **Goal:** Formalize the agreement between the two parties with a digital contract.
- **Dependencies:** Sprint 3.
- **Backend Tasks:**
  - `Task-B4.1`: Implement `Contract` model (linked to mission, employer, vacataire; status: `pending`, `signed`, `active`).
  - `Task-B4.2`: When an employer selects a candidate, generate a contract record and populate it from mission/user data.
  - `Task-B4.3`: Implement API endpoints to fetch a contract's details and to "sign" a contract (updates status).
  - `Task-B4.4`: Add business logic: only the selected vacataire and employer can view/sign.
- **Frontend Tasks:**
  - `Task-F4.1`: Build a `ContractReview` component that displays contract details in a read-only format.
  - `Task-F4.2`: Add an "Accept & Sign" button that calls the signing endpoint.
  - `Task-F4.3`: Update the mission status UI to reflect the contract status for both users.

### **Sprint 5: Timesheet & Payment Flow**

- **Goal:** Manage mission completion and trigger secure payments.
- **Dependencies:** Sprint 4.
- **Backend Tasks:**
  - `Task-B5.1`: Implement `Timesheet` model (linked to contract, hours_worked, status: `submitted`, `approved`, `paid`).
  - `Task-B5.2`: Implement API endpoints for timesheet submission and approval.
  - `Task-B5.3`: Integrate PayDunya/Stripe SDK.
  - `Task-B5.4`: Implement a use case/worker that, upon timesheet approval, creates a payment intent with the provider.
  - `Task-B5.5`: Implement a webhook endpoint to listen for payment success notifications from the provider and update the timesheet/contract status to `paid`.
- **Frontend Tasks:**
  - `Task-F5.1`: (Vacataire) Build a `TimesheetSubmit` form on the active mission page.
  - `Task-F5.2`: (Employer) Build a UI to review and approve submitted timesheets.
  - `Task-F5.3`: Display clear payment status updates to both users (`Payment Processing`, `Paid`).

### **Sprint 6: Evaluation System & Admin Panel**

- **Goal:** Allow users to build reputation and provide basic admin oversight.
- **Dependencies:** Sprint 5.
- **Backend Tasks:**
  - `Task-B6.1`: Implement `Evaluation` model (rating, comment, author, recipient, linked to mission).
  - `Task-B6.2`: Implement API endpoint to submit and view evaluations.
  - `Task-B6.3`: Implement basic admin-only endpoints to view/verify documents and manage users/missions.
  - `Task-B6.4`: Add an `is_admin` role and protect admin endpoints.
- **Frontend Tasks:**
  - `Task-F6.1`: Build an `Evaluation` form that appears after a mission is paid.
  - `Task-F6.2`: Display average ratings on user profiles.
  - `Task-F6.3`: Build a simple, access-controlled Admin Dashboard to list users needing verification, flag issues, etc.