# 2. Project Structure

## Purpose

To define the codebase organization for **OuiLink**, enforcing a Clean Architecture that promotes separation of concerns, testability, and long-term maintainability.

## Repository Layout (Backend)

- **`core/`**: The heart of the application. Contains no framework-specific code.
  - **`domain/`**: Business models (User, Mission, Contract, Document), value objects, and business rules.
  - **`use_cases/`**: Application-specific logic that orchestrates domain objects (e.g., `PostNewMission`, `ApplyToMission`, `SignContract`).
  - **`repositories/`**: Interfaces defining how to access data (e.g., `IMissionRepository`, `IUserRepository`).
- **`entry_points/`**: Connects the external world to the `core` use cases.
  - **`api/`**: Flask-based RESTful API controllers, request/response models, and dependency injection setup.
  - **`workers/`**: Background job definitions (e.g., sending notifications).
- **`infrastructure/`**: Concrete implementations of repository interfaces and external services.
  - **`persistence/`**: SQLAlchemy implementations of repositories, database models, and migrations.
  - **`services/`**: Clients for external services (e.g., PayDunya/Stripe, AWS S3 for storage).
- **`config/`**: Environment settings, dependency injection container setup.
- **`tests/`**: Automated tests.
  - **`unit/`**: Fast tests for domain and use cases with mocked dependencies.
  - **`integration/`**: Slower tests that involve real infrastructure (e.g., test database, external APIs).
- **`pyproject.toml`**: Python dependencies.

## Documentation Map (`docs/`)

- This directory (formerly `ouiassist/`) houses all project documentation.
- `1_PROJECT_REQUIREMENTS.md`: Project vision, scope, and goals.
- `2_PROJECT_STRUCTURE.md`: This file.
- `3_TECH_STACK.md`: All technologies used and their rationale.
- `4_PROJECT_GOVERNANCE.md`: Development phases, TDD policy, risk management.
- `5_USER_JOURNEY.md`: User personas, stories, and interaction flows.
- `6_DECISION_LOG.md`: A log of all major architectural and business decisions.
- `7_MVP_IMPLEMENTATION_ROADMAP.md`: The definitive step-by-step plan for building the MVP.

## AI Guidance: Clean Architecture

- **Domain layer must not depend on infrastructure or frameworks.**
- **Repositories implement interfaces defined in the domain.**
- **Entry points (web/API) only orchestrate use cases, never contain business logic.**
- **Tests must be isolated and repeatable.**
- **Documentation must be updated with every major code change.**
- **Use Cases are King:** Entry points should be thin. Their only job is to parse incoming requests, call a single use case, and format the response. All orchestration happens within the use case.
- **Repositories Abstract Persistence:** Use cases interact with repository interfaces, not concrete database implementations. This allows for easy testing and swapping of data sources.

## AI Firewalls (Red Flags)

- Do NOT import any module from `infrastructure` or `entry_points` into the `core` directory.
- Do NOT place business logic (if/else statements that decide *what* to do) inside API controllers or repository implementations.
- Do NOT create a pull request for a feature without corresponding unit and/or integration tests.
---

_Reference this document for codebase organization and documentation navigation. See also: Specs fonctionnelles MVP, Business Plan._
