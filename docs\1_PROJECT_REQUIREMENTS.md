# 1. Project Requirements & Vision

## Purpose

To define the strategic "why" and functional "what" of **OuiLink**, a digital platform designed to seamlessly connect healthcare facilities in Senegal with qualified, verified temporary healthcare professionals (vacataires).

## Problem Statement

Healthcare facilities in Senegal—from urban clinics to rural hospitals—face chronic and acute staffing shortages. The current process for finding temporary staff relies on informal, slow, and opaque personal networks (e.g., WhatsApp groups, word-of-mouth). This leads to:
- **Operational Inefficiency:** Delays in filling critical shifts, resulting in overworked staff and compromised patient care.
- **Lack of Trust:** Difficulty in verifying the qualifications and reliability of candidates.
- **Administrative Burden:** Time-consuming manual processes for contracting, scheduling, and payment.

## Target Audience
- Clinics, hospitals, and healthcare structures
- Healthcare professionals (doctors, nurses, technicians, paramedics)
- **Primary Users (The "Vacataires"):**
  - **Dr. <PERSON><PERSON><PERSON><PERSON><PERSON>:** A recently qualified General Practitioner in Dakar looking for flexible work to gain diverse experience before specializing.
  - **<PERSON><PERSON>:** An experienced registered nurse with 10+ years of experience who wants to supplement his primary income with weekend shifts.
- **Primary Users (The "Employers"):**
  - **M<PERSON>. <PERSON>rr:** The administrative manager of a private clinic in Thiès who needs to quickly find a replacement for a nurse on sick leave.
  - **Dr. Ba:** The Head of a regional hospital department facing a seasonal increase in patient load.

## Goals & Objectives

- Enable instant, secure matching between healthcare facilities and available professionals
- Provide a transparent, digital platform for contracts, payments, and scheduling
- Launch an MVP with essential features (profile creation, mission search, messaging)
- Ensure scalability for future expansion (other sectors, advanced features)
## Scope (MVP)

#### **Included Features:**

- **Identity & Profile Management:** Secure registration for both user types (vacataire, employer).
- **Secure Document Vault:** Vacataires can upload, and employers can view, verified credentials (diplomas, ID, medical license).
- **Mission Lifecycle Management:** Employers can post, manage, and close missions. Vacataires can search, filter, and apply for missions.
- **Internal Messaging:** Contextual, secure messaging linked to a specific mission application.
- **Digital Contracting:** Template-based contract generation with in-app review and e-signature capabilities.
- **Timesheet & Payment Flow:** Vacataires submit digital timesheets for approval. Employers approve timesheets and trigger secure payments via an integrated provider.
- **Two-Way Evaluation System:** Both parties rate and review each other after mission completion.

#### **Excluded Features (Post-MVP):**

- Advanced Analytics & Reporting Dashboard.
- Teleconsultation or other direct care services.
- Automated payroll and tax declaration services.
- Integration with external HR or insurance systems.
- Expansion to non-healthcare sectors.

## Success Criteria

- **Time-to-Fill:** Average time from mission posting to candidate acceptance is under 48 hours.
- **User Activation Rate:** >70% of registered users complete their profile (vacataires) or post their first mission (employers) within 7 days.
- **Platform Stickiness:** >80% of missions are re-listed or applied for by repeat users.
- **User Satisfaction:** Achieve an average rating of 4.5/5 stars in post-mission evaluations.
## Business Context & Value Proposition

- For clinics: Rapid access to competent professionals, reduced downtime, flexible staffing
- For vacataires: Diverse mission opportunities, clear payment management, professional recognition
- For the sector: Improved healthcare delivery, digital transformation, scalable model
## AI Guidance: Clean Code & Architecture

- **Domain logic must be isolated from infrastructure and UI.**
- **All business rules (e.g., profile completeness for application, valid contract state for payment) must reside in the domain layer.**
- **Never hardcode credentials or sensitive data; use environment variables and secrets management.**
- **All code must be typed using Python's type hinting.**

## AI Firewalls (Red Flags)

- Do NOT mix business logic with data access or presentation code.
- Do NOT bypass the defined validation rules for user profiles, documents, or missions.
- Do NOT bypass validation or error handling.
- Do NOT implement features outside the defined MVP scope without explicit instruction.
- Do NOT use non-idiomatic Python or ignore PEP8 standards.
- Always log implementation decisions and rationale in `6_DECISION_LOG.md`.

---

_Reference this document for all questions about project goals, scope, and success criteria. See also: Business Plan, Specs fonctionnelles MVP._